import { formatCurrency } from "../utils/format";
import { Link } from "react-router-dom";

const Card = ({ product }) => {
  return (
      <div className="p-4 bg-white product-card" style={{ borderRadius: "1rem", boxShadow: "0 0 15px rgba(0,0,0,0.3)"}}>
          <div className="w-full" style={{height: "15rem"}}>
              <img
                  src={product.image || "https://via.placeholder.com/150"}
                  alt={product.name}
                  style={{ borderRadius: "0.5rem", width: "100%" , height: "100%" , objectFit: "cover" }}
              />
          </div>

          <h5 className="text-lg font-semibold mt-2 ">{product.name} </h5>
          <p className="text-green-600 font-bold mt-2 ">{formatCurrency(product.price)}</p>

          <Link to={`/product/${product.id}`} className="w-100 btn mt-2" style={{ backgroundColor: "var(--bs-primary)", color: "white", fontWeight: "bold"}}>
            Xem chi tiết
          </Link>
      </div>
  );
};

export default Card;
